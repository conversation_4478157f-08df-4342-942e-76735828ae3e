:root {
    --primary-accent: #ff9900;
    --primary-accent-hover: #ffaf33;
    --background-dark: #1a1a1a;
    --background-card: #2c2c2c;
    --text-light: #f5f5f5;
    --text-secondary: #a0a0a0;
    --border-color: #444444;
    --risk-critical: #ff6b6b;
    --risk-high: #f0ad4e;
    --risk-medium: #5bc0de;
}

body {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.8;
    margin: 0;
    padding: 0;
    background-color: var(--background-dark);
    color: var(--text-light);
}



main {
    max-width: 900px;
    margin: 40px auto;
    padding: 20px;
}

header {
    text-align: center;
    padding: 20px;
    margin-bottom: 25px;
    background-color: #000000;
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

h1 {
    font-size: 2.8rem;
    font-weight: 700;
    color: var(--text-light);
    margin: 0;
}

h1 .accent-text {
    color: var(--primary-accent);
}

.subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-top: 10px;
}



/* Statistics */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: var(--background-card);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-accent);
    margin-bottom: 8px;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Filter controls */
.filter-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--background-card);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.filter-controls label {
    font-weight: 600;
    color: var(--text-light);
    margin-right: 8px;
}

.filter-controls select {
    background: var(--background-dark);
    color: var(--text-light);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 8px 12px;
}

.filter-controls select:focus {
    outline: none;
    border-color: var(--primary-accent);
}

.toc {
    background-color: var(--background-card);
    padding: 20px 25px;
    margin-bottom: 30px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.toc h3 {
    margin: 0 0 15px 0;
    font-size: 1.2rem;
    color: var(--primary-accent);
    text-align: center;
}

.toc ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
}

.toc a {
    text-decoration: none;
    font-weight: 700;
    color: var(--primary-accent);
    background-color: transparent;
    padding: 8px 18px;
    border-radius: 20px;
    border: 2px solid var(--primary-accent);
    transition: all 0.3s ease;
}

.toc a:hover {
    background-color: var(--primary-accent);
    color: #000000;
    box-shadow: 0 0 15px rgba(255, 153, 0, 0.5);
}

article > section, article > .cta-box {
    background-color: var(--background-card);
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    border: 1px solid var(--border-color);
}

h2 {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--primary-accent);
    padding-bottom: 15px;
    margin-top: 0;
    margin-bottom: 25px;
    border-bottom: 2px solid var(--border-color);
}

.section-intro { 
    color: var(--text-secondary); 
    font-style: italic; 
    font-size: 1.1rem; 
    margin-bottom: 25px; 
}

.cta-box { 
    background: var(--primary-accent); 
    text-align: center; 
}

.cta-box p { 
    margin: 0; 
    font-size: 1.2rem; 
    font-weight: 700; 
    color: #000; 
}

.cta-box .cta-link { 
    color: #000; 
    text-decoration: underline; 
}

.cta-box .cta-link:hover { 
    color: #333; 
}

.list-tier { 
    padding: 0; 
    border: none; 
    box-shadow: none; 
}

.list-tier h3 { 
    font-size: 1.6rem; 
    color: var(--text-light); 
    border-bottom: 1px solid var(--border-color); 
    padding-bottom: 12px; 
}

.tier-1-blacklist h3 { color: var(--risk-critical); }
.tier-2-sanctions h3 { color: var(--risk-high); }
.tier-3-grey h3 { color: var(--risk-medium); }

.table-container { 
    overflow-x: auto; 
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    min-width: 600px;
}

thead { 
    background-color: #333333; 
}

th, td { 
    padding: 14px 16px; 
    text-align: left; 
    border-bottom: 1px solid var(--border-color); 
}

th { 
    font-weight: 700; 
    font-size: 1rem; 
    color: var(--primary-accent); 
}

tbody tr:nth-child(even) { 
    background-color: #303030; 
}

tbody tr:hover {
    background-color: rgba(255, 153, 0, 0.1);
}

/* Risk badges */
.risk-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
}

.risk-critical {
    color: var(--risk-critical);
    background: rgba(255, 107, 107, 0.2);
}

.risk-high {
    color: var(--risk-high);
    background: rgba(240, 173, 78, 0.2);
}

.risk-medium {
    color: var(--risk-medium);
    background: rgba(91, 192, 222, 0.2);
}

.risk-note {
    margin-left: 8px;
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-style: italic;
}

.risk-assessment {
    margin-top: 15px;
    padding: 15px;
    background: rgba(255, 153, 0, 0.1);
    border-radius: 8px;
    border-left: 4px solid var(--primary-accent);
}

.case-study {
    background-color: #333333;
    border: 1px solid #555555;
    padding: 25px;
    margin-top: 25px;
    border-radius: 8px;
}

.case-study h4 { 
    margin-top: 0; 
    font-size: 1.4rem; 
    color: var(--text-light); 
}

/* Checklist grid */
.checklist-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.checklist-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 20px;
    background: #333333;
    border-radius: 8px;
    border: 1px solid #555555;
}

.checklist-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.checklist-content strong {
    color: var(--primary-accent);
    display: block;
    margin-bottom: 8px;
}

/* Verification steps */
.verification-steps {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-top: 20px;
}

.step-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 25px;
    background: #333333;
    border-radius: 8px;
    border: 1px solid #555555;
}

.step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--primary-accent);
    color: #000;
    font-weight: 700;
    font-size: 1.2rem;
    border-radius: 50%;
    flex-shrink: 0;
}

.step-content h4 {
    margin: 0 0 10px 0;
    font-size: 1.1rem;
    color: var(--text-light);
}

.step-link {
    color: var(--primary-accent);
    text-decoration: none;
    font-weight: 600;
}

.step-link:hover {
    text-decoration: underline;
}

/* Resources grid */
.resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.resource-card {
    background: #333333;
    padding: 25px;
    border-radius: 8px;
    border: 1px solid #555555;
}

.resource-card h4 {
    margin: 0 0 15px 0;
    font-size: 1.2rem;
    color: var(--text-light);
}

.resource-card ul {
    list-style: none;
    padding: 0;
}

.resource-card li {
    margin-bottom: 10px;
    padding-left: 20px;
    position: relative;
}

.resource-card li::before {
    content: '→';
    position: absolute;
    left: 0;
    color: var(--primary-accent);
    font-weight: 700;
}

.resource-card a {
    color: var(--text-light);
    text-decoration: none;
}

.resource-card a:hover {
    color: var(--primary-accent);
}

/* Lesson highlight */
.lesson-highlight {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-top: 20px;
    padding: 20px;
    background: rgba(255, 153, 0, 0.1);
    border-radius: 8px;
    border: 1px solid var(--primary-accent);
}

.lesson-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.lesson-text {
    font-weight: 600;
    color: var(--primary-accent);
}

/* Back to top button */
.back-to-top-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-accent);
    color: #000;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    font-weight: 700;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
}

.back-to-top-btn.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top-btn:hover {
    background: var(--primary-accent-hover);
    transform: translateY(-2px);
}

footer {
    text-align: center;
    margin-top: 40px;
    padding: 20px;
    color: var(--text-secondary);
}

.footer-links {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
    margin: 20px 0;
}

.footer-links a {
    color: var(--primary-accent);
    text-decoration: none;
    font-weight: 600;
}

.footer-links a:hover {
    text-decoration: underline;
}

.copyright {
    font-size: 0.9rem;
    color: #666666;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}

/* Utility classes */
.hidden {
    display: none !important;
}

/* Responsive design */
@media (max-width: 768px) {
    main { 
        padding: 10px; 
        margin: 10px auto; 
    }
    
    h1 { 
        font-size: 2rem; 
    }
    
    h2 { 
        font-size: 1.8rem; 
    }
    
    article > section, article > .cta-box { 
        padding: 20px; 
    }
    
    .toc ul { 
        flex-direction: column; 
        gap: 10px; 
    }
    
    .toc a { 
        width: 100%; 
        text-align: center; 
        box-sizing: border-box; 
    }
    

    
    .filter-controls {
        flex-direction: column;
        gap: 15px;
    }
    
    .checklist-grid {
        grid-template-columns: 1fr;
    }
    
    .verification-steps .step-item {
        flex-direction: column;
        text-align: center;
    }
    
    .resources-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-container {
        grid-template-columns: 1fr;
    }
    
    .back-to-top-btn {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
    }
    
    .footer-links {
        flex-direction: column;
        gap: 10px;
    }
}
