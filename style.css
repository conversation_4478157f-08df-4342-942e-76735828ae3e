:root {
    --primary-accent: #ff9900; /* Bright Orange */
    --primary-accent-hover: #ffaf33;
    --background-dark: #1a1a1a;
    --background-card: #2c2c2c;
    --text-light: #f5f5f5;
    --text-secondary: #a0a0a0;
    --border-color: #444444;
}

body {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.8;
    margin: 0;
    padding: 0;
    background-color: var(--background-dark);
    color: var(--text-light);
}

main {
    max-width: 900px;
    margin: 40px auto;
    padding: 20px;
    background-color: transparent; /* Main container is transparent */
}

header {
    text-align: center;
    padding: 20px;
    margin-bottom: 25px;
    background-color: #000000;
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

h1 {
    font-size: 2.8rem;
    font-weight: 700;
    color: var(--text-light);
    margin: 0;
}

h1 .accent-text {
    color: var(--primary-accent);
}

.subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-top: 10px;
}

.toc {
    background-color: var(--background-card);
    padding: 20px 25px;
    margin-bottom: 30px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.toc h3 {
    margin: 0 0 15px 0;
    font-size: 1.2rem;
    color: var(--primary-accent);
    text-align: center;
}

.toc ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
}

.toc a {
    text-decoration: none;
    font-weight: 700;
    color: var(--primary-accent);
    background-color: transparent;
    padding: 8px 18px;
    border-radius: 20px;
    border: 2px solid var(--primary-accent);
    transition: all 0.3s ease;
}

.toc a:hover {
    background-color: var(--primary-accent);
    color: #000000;
    box-shadow: 0 0 15px rgba(255, 153, 0, 0.5);
}

article > section, article > .cta-box {
    background-color: var(--background-card);
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    border: 1px solid var(--border-color);
}

h2 {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--primary-accent);
    padding-bottom: 15px;
    margin-top: 0;
    margin-bottom: 25px;
    border-bottom: 2px solid var(--border-color);
}

.section-intro { color: var(--text-secondary); font-style: italic; font-size: 1.1rem; margin-bottom: 25px; }

.cta-box { background: var(--primary-accent); text-align: center; }
.cta-box p { margin: 0; font-size: 1.2rem; font-weight: 700; color: #000; }
.cta-box .cta-link { color: #000; text-decoration: underline; }
.cta-box .cta-link:hover { color: #333; }

.list-tier { padding: 0; border: none; box-shadow: none; }
.list-tier h3 { font-size: 1.6rem; color: var(--text-light); border-bottom: 1px solid var(--border-color); padding-bottom: 12px; }

.tier-1-blacklist h3 { color: #ff6b6b; } /* Red for blacklist */
.tier-2-sanctions h3 { color: #f0ad4e; } /* Yellow for sanctions */
.tier-3-grey h3 { color: #5bc0de; } /* Blue for grey area */

.table-container { overflow-x: auto; }

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    min-width: 600px;
}

thead { background-color: #333333; }

th, td { padding: 14px 16px; text-align: left; border-bottom: 1px solid var(--border-color); }

th { font-weight: 700; font-size: 1rem; color: var(--primary-accent); }

tbody tr:nth-child(even) { background-color: #303030; }

.case-study {
    background-color: #333333;
    border: 1px solid #555555;
    padding: 25px;
    margin-top: 25px;
    border-radius: 8px;
}

.case-study h4 { margin-top: 0; font-size: 1.4rem; color: var(--text-light); }

.content-box { padding: 0; border: none; }

ol li { margin-bottom: 15px; padding-left: 10px; }
ol li::marker { font-weight: 700; color: var(--primary-accent); }

ul { list-style: none; padding-left: 0; }
ul li {
    padding-left: 25px;
    position: relative;
    margin-bottom: 12px;
}
ul li::before {
    content: '»';
    position: absolute;
    left: 0;
    color: var(--primary-accent);
    font-weight: 700;
}

a { color: var(--primary-accent); text-decoration: none; }
a:hover { text-decoration: underline; color: var(--primary-accent-hover); }

strong { color: var(--primary-accent); }

footer {
    text-align: center;
    margin-top: 40px;
    padding: 20px;
    color: var(--text-secondary);
}

.back-to-top { display: inline-block; margin-bottom: 15px; font-weight: 700; }

@media (max-width: 768px) {
    main { padding: 10px; margin: 10px auto; }
    h1 { font-size: 2rem; }
    h2 { font-size: 1.8rem; }
    article > section, article > .cta-box { padding: 20px; }
    .toc ul { flex-direction: column; gap: 10px; }
    .toc a { width: 100%; text-align: center; box-sizing: border-box; }
}