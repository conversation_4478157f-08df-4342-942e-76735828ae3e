{"extends": ["stylelint-config-standard"], "rules": {"indentation": 4, "string-quotes": "single", "no-duplicate-selectors": true, "color-hex-case": "lower", "color-hex-length": "short", "color-named": "never", "selector-combinator-space-after": "always", "selector-attribute-quotes": "always", "selector-attribute-operator-space-before": "never", "selector-attribute-operator-space-after": "never", "selector-attribute-brackets-space-inside": "never", "declaration-block-trailing-semicolon": "always", "declaration-colon-space-before": "never", "declaration-colon-space-after": "always", "property-no-vendor-prefix": true, "value-no-vendor-prefix": true, "number-leading-zero": "always", "function-url-quotes": "always", "font-weight-notation": "numeric", "comment-whitespace-inside": "always", "at-rule-no-vendor-prefix": true, "rule-empty-line-before": ["always", {"except": ["first-nested"], "ignore": ["after-comment"]}], "selector-pseudo-element-colon-notation": "double"}}