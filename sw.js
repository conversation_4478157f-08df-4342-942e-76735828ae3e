/**
 * Service Worker for Education Risk Guide PWA
 * Provides offline functionality and caching
 */

const CACHE_NAME = 'education-guide-v1.0.0';
const STATIC_CACHE_NAME = 'education-guide-static-v1.0.0';
const DYNAMIC_CACHE_NAME = 'education-guide-dynamic-v1.0.0';

// Files to cache for offline functionality
const STATIC_FILES = [
    '/',
    '/index.html',
    '/style.css',
    '/script.js',
    '/manifest.json',
    '/robots.txt',
    '/sitemap.xml',
    // Add optimized versions if they exist
    '/index_optimized.html',
    '/style_optimized.css',
    // Google Fonts (will be cached dynamically)
    'https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap',
    // Fallback offline page
    '/offline.html'
];

// URLs that should always be fetched from network
const NETWORK_FIRST_URLS = [
    'http://jsj.moe.gov.cn/',
    'https://www.ed.gov/',
    'https://www.chea.org/',
    'https://www.officeforstudents.org.uk/',
    'https://www.teqsa.gov.au/'
];

// Install event - cache static files
self.addEventListener('install', (event) => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE_NAME)
            .then((cache) => {
                console.log('Service Worker: Caching static files');
                return cache.addAll(STATIC_FILES.filter(url => !url.startsWith('http')));
            })
            .then(() => {
                console.log('Service Worker: Static files cached');
                return self.skipWaiting();
            })
            .catch((error) => {
                console.error('Service Worker: Error caching static files', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys()
            .then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        if (cacheName !== STATIC_CACHE_NAME && 
                            cacheName !== DYNAMIC_CACHE_NAME &&
                            cacheName.startsWith('education-guide-')) {
                            console.log('Service Worker: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker: Activated');
                return self.clients.claim();
            })
    );
});

// Fetch event - handle requests with different strategies
self.addEventListener('fetch', (event) => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }
    
    // Skip chrome-extension and other non-http(s) requests
    if (!request.url.startsWith('http')) {
        return;
    }
    
    // Handle different types of requests
    if (NETWORK_FIRST_URLS.some(networkUrl => request.url.startsWith(networkUrl))) {
        // Network first for external official resources
        event.respondWith(networkFirst(request));
    } else if (request.destination === 'document') {
        // Cache first for HTML documents
        event.respondWith(cacheFirst(request));
    } else if (request.destination === 'style' || request.destination === 'script') {
        // Cache first for CSS and JS
        event.respondWith(cacheFirst(request));
    } else if (request.destination === 'font') {
        // Cache first for fonts
        event.respondWith(cacheFirst(request));
    } else if (request.destination === 'image') {
        // Cache first for images
        event.respondWith(cacheFirst(request));
    } else {
        // Network first for other resources
        event.respondWith(networkFirst(request));
    }
});

/**
 * Cache first strategy
 * Try cache first, fallback to network
 */
async function cacheFirst(request) {
    try {
        // Try to get from cache first
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // If not in cache, fetch from network
        const networkResponse = await fetch(request);
        
        // Cache the response for future use
        if (networkResponse.status === 200) {
            const cache = await caches.open(DYNAMIC_CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.error('Cache first strategy failed:', error);
        
        // Return offline fallback for HTML documents
        if (request.destination === 'document') {
            const offlineResponse = await caches.match('/offline.html');
            return offlineResponse || new Response('Offline', { status: 503 });
        }
        
        // Return empty response for other resources
        return new Response('', { status: 503 });
    }
}

/**
 * Network first strategy
 * Try network first, fallback to cache
 */
async function networkFirst(request) {
    try {
        // Try network first
        const networkResponse = await fetch(request);
        
        // Cache successful responses
        if (networkResponse.status === 200) {
            const cache = await caches.open(DYNAMIC_CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.error('Network first strategy failed:', error);
        
        // Fallback to cache
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Return offline fallback for HTML documents
        if (request.destination === 'document') {
            const offlineResponse = await caches.match('/offline.html');
            return offlineResponse || new Response('Offline', { status: 503 });
        }
        
        // Return empty response for other resources
        return new Response('', { status: 503 });
    }
}

/**
 * Background sync for future enhancements
 */
self.addEventListener('sync', (event) => {
    console.log('Service Worker: Background sync', event.tag);
    
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

/**
 * Handle background sync
 */
async function doBackgroundSync() {
    try {
        // Implement background sync logic here
        // For example, sync offline form submissions
        console.log('Service Worker: Performing background sync');
    } catch (error) {
        console.error('Service Worker: Background sync failed', error);
    }
}

/**
 * Handle push notifications (for future enhancements)
 */
self.addEventListener('push', (event) => {
    console.log('Service Worker: Push received', event);
    
    const options = {
        body: event.data ? event.data.text() : 'New update available',
        icon: '/icons/icon-192x192.png',
        badge: '/icons/icon-72x72.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: 'View Update',
                icon: '/icons/checkmark.png'
            },
            {
                action: 'close',
                title: 'Close',
                icon: '/icons/xmark.png'
            }
        ]
    };
    
    event.waitUntil(
        self.registration.showNotification('Education Risk Guide', options)
    );
});

/**
 * Handle notification clicks
 */
self.addEventListener('notificationclick', (event) => {
    console.log('Service Worker: Notification clicked', event);
    
    event.notification.close();
    
    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});

/**
 * Handle messages from main thread
 */
self.addEventListener('message', (event) => {
    console.log('Service Worker: Message received', event.data);
    
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'GET_VERSION') {
        event.ports[0].postMessage({ version: CACHE_NAME });
    }
});

/**
 * Periodic background sync (for future enhancements)
 */
self.addEventListener('periodicsync', (event) => {
    console.log('Service Worker: Periodic sync', event.tag);
    
    if (event.tag === 'content-sync') {
        event.waitUntil(syncContent());
    }
});

/**
 * Sync content in background
 */
async function syncContent() {
    try {
        // Implement periodic content sync
        console.log('Service Worker: Syncing content');
    } catch (error) {
        console.error('Service Worker: Content sync failed', error);
    }
}

/**
 * Clean up old cache entries
 */
async function cleanupCache() {
    const cache = await caches.open(DYNAMIC_CACHE_NAME);
    const requests = await cache.keys();
    
    // Remove old entries (keep last 50)
    if (requests.length > 50) {
        const requestsToDelete = requests.slice(0, requests.length - 50);
        await Promise.all(
            requestsToDelete.map(request => cache.delete(request))
        );
    }
}

// Cleanup cache periodically
setInterval(cleanupCache, 24 * 60 * 60 * 1000); // Once per day
