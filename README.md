# 海外高等教育风险识别与院校甄别权威指南

这是一个开源的、静态的网页项目，旨在成为一份全面、权威的指南，帮助计划出国留学的学生、家长及教育工作者识别海外高等教育中存在的风险，特别是“文凭工厂”（Diploma Mill）和各类资质存疑的院校。

本指南深入剖析了教育欺诈的运作模式，系统梳理了中国及主要留学目的地国的官方认证体系，并基于公开数据建立了一个包含多个风险等级的警示院校索引。

**项目的核心目标是提供一套行之有效的尽职调查方案，赋能用户做出明智、安全的教育选择。**

## 🚀 在线访问 (Live Demo)

您可以通过 Cloudflare Pages 轻松部署此项目，并获得一个免费的公开访问网址。部署完成后的网站将是一份结构清晰、内容详实的在线指南。

## 部署到 Cloudflare Pages

Cloudflare Pages 是一个免费、高速的静态网站托管平台，与GitHub完美集成。部署过程非常简单，通常在5分钟内即可完成。

**前提：**
*   你有一个 GitHub 账户。
*   你有一个 Cloudflare 账户 (免费注册)。

### 步骤 1: Fork 或创建你自己的仓库

1.  **Fork 本项目**: 点击本项目页面右上角的 "Fork" 按钮，将此项目复制到你自己的 GitHub 账户下。
2.  **（或者）创建新仓库并上传文件**: 
    *   在 GitHub 上创建一个新的空仓库 (例如, `education-warning-guide`)。
    *   将 `index.html`, `style.css`, 和 `README.md` 文件上传到你的新仓库中。你可以使用以下 `git` 命令：

```bash
# 在你的本地项目文件夹中 (C:\Users\<USER>\Desktop\vs\edu)
git init
git add .
git commit -m "Initial commit: Add comprehensive education warning guide"

# 关联到你的GitHub远程仓库 (请替换 <your-username> 和 <your-repo-name>)
git remote add origin https://github.com/<your-username>/<your-repo-name>.git
git branch -M main
git push -u origin main
```

### 步骤 2: 在 Cloudflare 创建新项目

1.  登录到你的 Cloudflare 仪表板。
2.  在左侧导航栏中，进入 **Workers & Pages**。
3.  点击 **Create application** -> 选择 **Pages** -> 点击 **Connect to Git**。
4.  选择你刚刚创建或 Fork 的 GitHub 仓库，然后点击 **Begin setup**。

### 步骤 3: 配置构建和部署

Cloudflare 会自动检测到这是一个静态网站项目，因此你几乎不需要进行任何配置。

*   **Project name**: 可以保持默认（你的仓库名）或自定义。
*   **Production branch**: 选择 `main`。
*   **Build settings**: **将“框架预设” (Framework preset) 设置为 `None`**。这是最重要的一步，因为我们没有使用任何前端框架。
*   **Build command**: 留空。
*   **Build output directory**: 留空。

### 步骤 4: 部署！

1.  点击 **Save and Deploy**。
2.  Cloudflare 将在几秒钟内完成部署，并为你提供一个唯一的网址 (例如 `https://<project-name>.pages.dev`)。

现在，你的海外大学风险甄别权威指南网站已经成功上线！

## 🤝 如何贡献

如果你发现名单中有需要补充或更正的信息，或有新的案例可以分享，欢迎通过以下方式贡献：

1.  **Fork** 项目仓库。
2.  在你的分支上进行修改。
3.  创建一个 **Pull Request**，并提供可靠的信息来源链接（例如官方公告、权威媒体报道等）。

## 📝 免责声明

本指南所有信息均来自公开渠道，仅供参考，不构成法律或教育建议。在做出任何重大教育决策之前，请务必通过本指南中提到的官方权威机构进行最终核实。