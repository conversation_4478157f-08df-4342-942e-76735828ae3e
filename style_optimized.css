/* ===== CSS VARIABLES ===== */
:root {
    /* Colors */
    --primary-accent: #ff9900;
    --primary-accent-hover: #ffaf33;
    --primary-accent-light: rgba(255, 153, 0, 0.1);
    --background-dark: #1a1a1a;
    --background-card: #2c2c2c;
    --background-card-hover: #333333;
    --text-light: #f5f5f5;
    --text-secondary: #a0a0a0;
    --text-muted: #666666;
    --border-color: #444444;
    --border-color-light: #555555;
    
    /* Risk Colors */
    --risk-critical: #ff6b6b;
    --risk-high: #f0ad4e;
    --risk-medium: #5bc0de;
    --risk-low: #5cb85c;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
    
    /* Typography */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 2rem;
    --font-size-4xl: 2.5rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-base: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-full: 9999px;
}

/* ===== RESET & BASE STYLES ===== */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.7;
    color: var(--text-light);
    background-color: var(--background-dark);
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(255, 153, 0, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 153, 0, 0.03) 0%, transparent 50%);
    background-attachment: fixed;
    overflow-x: hidden;
}

/* ===== ACCESSIBILITY ===== */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-accent);
    color: #000;
    padding: 8px;
    text-decoration: none;
    border-radius: var(--radius-md);
    z-index: 1000;
    font-weight: 600;
    transition: var(--transition-fast);
}

.skip-link:focus {
    top: 6px;
}

/* ===== LAYOUT ===== */
main {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-xl);
    position: relative;
}

/* ===== HEADER ===== */
header {
    text-align: center;
    padding: var(--spacing-xxl) var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 153, 0, 0.05) 50%, transparent 70%);
    pointer-events: none;
}

h1 {
    font-size: clamp(var(--font-size-3xl), 5vw, var(--font-size-4xl));
    font-weight: 700;
    color: var(--text-light);
    margin-bottom: var(--spacing-md);
    position: relative;
    z-index: 1;
}

h1 .accent-text {
    color: var(--primary-accent);
    position: relative;
}

h1 .accent-text::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-accent), var(--primary-accent-hover));
    border-radius: var(--radius-full);
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { box-shadow: 0 0 5px var(--primary-accent); }
    to { box-shadow: 0 0 20px var(--primary-accent), 0 0 30px var(--primary-accent); }
}

.subtitle {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    position: relative;
    z-index: 1;
}

/* ===== SEARCH CONTAINER ===== */
.search-container {
    display: flex;
    max-width: 500px;
    margin: 0 auto;
    background: var(--background-card);
    border-radius: var(--radius-full);
    padding: var(--spacing-xs);
    border: 2px solid var(--border-color);
    transition: var(--transition-base);
    position: relative;
    z-index: 1;
}

.search-container:focus-within {
    border-color: var(--primary-accent);
    box-shadow: 0 0 0 3px var(--primary-accent-light);
}

#search-input {
    flex: 1;
    background: transparent;
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--text-light);
    font-size: var(--font-size-base);
    outline: none;
}

#search-input::placeholder {
    color: var(--text-muted);
}

#search-btn {
    background: var(--primary-accent);
    border: none;
    padding: var(--spacing-md);
    border-radius: var(--radius-full);
    cursor: pointer;
    transition: var(--transition-base);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    min-height: 44px;
}

#search-btn:hover {
    background: var(--primary-accent-hover);
    transform: scale(1.05);
}

#search-btn:active {
    transform: scale(0.95);
}

/* ===== NAVIGATION ===== */
.toc {
    background: var(--background-card);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-md);
    transition: var(--transition-base);
}

.toc:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.toc h3 {
    margin: 0 0 var(--spacing-lg) 0;
    font-size: var(--font-size-xl);
    color: var(--primary-accent);
    text-align: center;
    font-weight: 600;
}

.toc ul {
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--spacing-md);
}

.toc a {
    text-decoration: none;
    font-weight: 600;
    color: var(--primary-accent);
    background: transparent;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-full);
    border: 2px solid var(--primary-accent);
    transition: var(--transition-base);
    display: inline-block;
    position: relative;
    overflow: hidden;
}

.toc a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--primary-accent);
    transition: var(--transition-base);
    z-index: -1;
}

.toc a:hover::before {
    left: 0;
}

.toc a:hover {
    color: #000000;
    box-shadow: 0 0 20px rgba(255, 153, 0, 0.3);
    transform: translateY(-2px);
}

/* ===== SECTIONS ===== */
article > section,
article > .cta-box {
    background: var(--background-card);
    padding: var(--spacing-xxl);
    border-radius: var(--radius-xl);
    margin-bottom: var(--spacing-xl);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-md);
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
}

article > section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, var(--primary-accent), var(--primary-accent-hover));
    opacity: 0;
    transition: var(--transition-base);
}

article > section:hover::before {
    opacity: 1;
}

article > section:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

h2 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--primary-accent);
    padding-bottom: var(--spacing-lg);
    margin-top: 0;
    margin-bottom: var(--spacing-xl);
    border-bottom: 2px solid var(--border-color);
    position: relative;
}

h2::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: var(--primary-accent);
    border-radius: var(--radius-full);
}

.section-intro {
    color: var(--text-secondary);
    font-style: italic;
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: rgba(255, 153, 0, 0.05);
    border-left: 4px solid var(--primary-accent);
    border-radius: var(--radius-md);
}

/* ===== CTA BOX ===== */
.cta-box {
    background: linear-gradient(135deg, var(--primary-accent) 0%, var(--primary-accent-hover) 100%);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.cta-box::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(255, 255, 255, 0.05) 10px,
        rgba(255, 255, 255, 0.05) 20px
    );
    animation: slide 20s linear infinite;
}

@keyframes slide {
    0% { transform: translateX(-50px) translateY(-50px); }
    100% { transform: translateX(50px) translateY(50px); }
}

.cta-box p {
    margin: 0;
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: #000;
    position: relative;
    z-index: 1;
}

.cta-box .cta-link {
    color: #000;
    text-decoration: underline;
    transition: var(--transition-base);
}

.cta-box .cta-link:hover {
    color: #333;
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

/* ===== STATISTICS ===== */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
}

.stat-item {
    text-align: center;
    padding: var(--spacing-lg);
    background: var(--background-card-hover);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color-light);
    transition: var(--transition-base);
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stat-number {
    display: block;
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--primary-accent);
    margin-bottom: var(--spacing-sm);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

/* ===== FILTER CONTROLS ===== */
.filter-controls {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--background-card-hover);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color-light);
}

.filter-controls label {
    font-weight: 600;
    color: var(--text-light);
    margin-right: var(--spacing-sm);
}

.filter-controls select {
    background: var(--background-dark);
    color: var(--text-light);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    transition: var(--transition-base);
}

.filter-controls select:focus {
    outline: none;
    border-color: var(--primary-accent);
    box-shadow: 0 0 0 2px var(--primary-accent-light);
}

/* ===== TABLES ===== */
.list-tier {
    padding: 0;
    border: none;
    box-shadow: none;
    margin-bottom: var(--spacing-xl);
}

.list-tier h3 {
    font-size: var(--font-size-2xl);
    color: var(--text-light);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    position: relative;
}

.tier-1-blacklist h3 { color: var(--risk-critical); }
.tier-2-sanctions h3 { color: var(--risk-high); }
.tier-3-grey h3 { color: var(--risk-medium); }

.table-container {
    overflow-x: auto;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    background: var(--background-card-hover);
}

table {
    width: 100%;
    border-collapse: collapse;
    min-width: 700px;
    background: transparent;
}

thead {
    background: linear-gradient(135deg, #333333 0%, #2a2a2a 100%);
    position: sticky;
    top: 0;
    z-index: 10;
}

th, td {
    padding: var(--spacing-lg);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition-base);
}

th {
    font-weight: 700;
    font-size: var(--font-size-base);
    color: var(--primary-accent);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

tbody tr {
    transition: var(--transition-base);
}

tbody tr:nth-child(even) {
    background: rgba(255, 255, 255, 0.02);
}

tbody tr:hover {
    background: rgba(255, 153, 0, 0.1);
    transform: scale(1.01);
}

tbody tr:hover td {
    color: var(--text-light);
}

/* ===== RISK BADGES ===== */
.risk-badge {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 2px solid currentColor;
}

.risk-critical {
    color: var(--risk-critical);
    background: rgba(255, 107, 107, 0.1);
}

.risk-high {
    color: var(--risk-high);
    background: rgba(240, 173, 78, 0.1);
}

.risk-medium {
    color: var(--risk-medium);
    background: rgba(91, 192, 222, 0.1);
}

.risk-low {
    color: var(--risk-low);
    background: rgba(92, 184, 92, 0.1);
}

.risk-note {
    display: inline-block;
    margin-left: var(--spacing-sm);
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    font-style: italic;
}

.risk-assessment {
    margin-top: var(--spacing-md);
    padding: var(--spacing-md);
    background: rgba(255, 153, 0, 0.05);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--primary-accent);
}

/* ===== CASE STUDIES ===== */
.case-study {
    background: linear-gradient(135deg, #333333 0%, #2a2a2a 100%);
    border: 1px solid var(--border-color-light);
    padding: var(--spacing-xl);
    margin-top: var(--spacing-xl);
    border-radius: var(--radius-lg);
    position: relative;
    overflow: hidden;
}

.case-study::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-accent), var(--primary-accent-hover));
}

.case-study h4 {
    margin-top: 0;
    font-size: var(--font-size-xl);
    color: var(--text-light);
    margin-bottom: var(--spacing-lg);
}

.lesson-highlight {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: rgba(255, 153, 0, 0.1);
    border-radius: var(--radius-lg);
    border: 1px solid var(--primary-accent);
}

.lesson-icon {
    font-size: var(--font-size-xl);
    flex-shrink: 0;
}

.lesson-text {
    font-weight: 600;
    color: var(--primary-accent);
}

/* ===== CHECKLIST ===== */
.checklist-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.checklist-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: var(--background-card-hover);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color-light);
    transition: var(--transition-base);
}

.checklist-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-accent);
}

.checklist-icon {
    font-size: var(--font-size-xl);
    flex-shrink: 0;
    margin-top: var(--spacing-xs);
}

.checklist-content strong {
    color: var(--primary-accent);
    display: block;
    margin-bottom: var(--spacing-sm);
}

/* ===== VERIFICATION STEPS ===== */
.verification-steps {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.step-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-lg);
    padding: var(--spacing-xl);
    background: var(--background-card-hover);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color-light);
    transition: var(--transition-base);
    position: relative;
}

.step-item:hover {
    transform: translateX(10px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-accent);
}

.step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-accent) 0%, var(--primary-accent-hover) 100%);
    color: #000;
    font-weight: 700;
    font-size: var(--font-size-xl);
    border-radius: var(--radius-full);
    flex-shrink: 0;
    box-shadow: var(--shadow-md);
}

.step-content h4 {
    margin: 0 0 var(--spacing-md) 0;
    font-size: var(--font-size-lg);
    color: var(--text-light);
}

.step-content p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

.step-link {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--primary-accent);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-base);
}

.step-link:hover {
    color: var(--primary-accent-hover);
    transform: translateX(5px);
}

/* ===== RESOURCES GRID ===== */
.resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.resource-card {
    background: var(--background-card-hover);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color-light);
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
}

.resource-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-accent), var(--primary-accent-hover));
    transform: scaleX(0);
    transition: var(--transition-base);
}

.resource-card:hover::before {
    transform: scaleX(1);
}

.resource-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.resource-card h4 {
    margin: 0 0 var(--spacing-lg) 0;
    font-size: var(--font-size-lg);
    color: var(--text-light);
}

.resource-card ul {
    list-style: none;
}

.resource-card li {
    margin-bottom: var(--spacing-md);
    position: relative;
    padding-left: var(--spacing-lg);
}

.resource-card li::before {
    content: '→';
    position: absolute;
    left: 0;
    color: var(--primary-accent);
    font-weight: 700;
    transition: var(--transition-base);
}

.resource-card li:hover::before {
    transform: translateX(3px);
}

.resource-card a {
    color: var(--text-light);
    text-decoration: none;
    transition: var(--transition-base);
}

.resource-card a:hover {
    color: var(--primary-accent);
}

/* ===== BACK TO TOP BUTTON ===== */
.back-to-top-btn {
    position: fixed;
    bottom: var(--spacing-xl);
    right: var(--spacing-xl);
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-accent) 0%, var(--primary-accent-hover) 100%);
    color: #000;
    border: none;
    border-radius: var(--radius-full);
    cursor: pointer;
    font-size: var(--font-size-xl);
    font-weight: 700;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-base);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
}

.back-to-top-btn.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top-btn:hover {
    transform: translateY(-5px) scale(1.1);
    box-shadow: var(--shadow-xl);
}

.back-to-top-btn:active {
    transform: translateY(-2px) scale(1.05);
}

/* ===== FOOTER ===== */
footer {
    margin-top: var(--spacing-xxl);
    padding: var(--spacing-xxl);
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-color);
    text-align: center;
    color: var(--text-secondary);
}

.footer-content p {
    margin-bottom: var(--spacing-md);
}

.footer-links {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
}

.footer-links a {
    color: var(--primary-accent);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-base);
}

.footer-links a:hover {
    color: var(--primary-accent-hover);
    text-decoration: underline;
}

.copyright {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

/* ===== UTILITY CLASSES ===== */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    :root {
        --spacing-xl: 1rem;
        --spacing-xxl: 1.5rem;
    }

    main {
        padding: var(--spacing-md);
    }

    header {
        padding: var(--spacing-xl);
    }

    h1 {
        font-size: var(--font-size-3xl);
    }

    h2 {
        font-size: var(--font-size-2xl);
    }

    article > section,
    article > .cta-box {
        padding: var(--spacing-xl);
    }

    .toc ul {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .toc a {
        width: 100%;
        text-align: center;
        box-sizing: border-box;
    }

    .search-container {
        max-width: 100%;
    }

    .filter-controls {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .checklist-grid {
        grid-template-columns: 1fr;
    }

    .verification-steps .step-item {
        flex-direction: column;
        text-align: center;
    }

    .resources-grid {
        grid-template-columns: 1fr;
    }

    .stats-container {
        grid-template-columns: 1fr;
    }

    .back-to-top-btn {
        bottom: var(--spacing-md);
        right: var(--spacing-md);
        width: 45px;
        height: 45px;
    }

    .footer-links {
        flex-direction: column;
        gap: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .table-container {
        font-size: var(--font-size-sm);
    }

    th, td {
        padding: var(--spacing-md);
    }

    .step-number {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-lg);
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    body {
        background: white;
        color: black;
    }

    .search-container,
    .back-to-top-btn,
    .filter-controls {
        display: none;
    }

    article > section,
    article > .cta-box {
        box-shadow: none;
        border: 1px solid #ccc;
        break-inside: avoid;
    }

    .toc {
        break-after: page;
    }
}

/* ===== DARK MODE ENHANCEMENTS ===== */
@media (prefers-color-scheme: dark) {
    /* Already optimized for dark mode */
}

/* ===== REDUCED MOTION ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    html {
        scroll-behavior: auto;
    }
}
