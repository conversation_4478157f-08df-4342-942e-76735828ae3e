/**
 * Simple Education Risk Guide JavaScript
 * Essential functionality for search, filters, and navigation
 */

document.addEventListener('DOMContentLoaded', function() {
    // Cache DOM elements
    const searchInput = document.getElementById('search-input');
    const searchBtn = document.getElementById('search-btn');
    const countryFilter = document.getElementById('country-filter');
    const riskFilter = document.getElementById('risk-filter');
    const backToTopBtn = document.getElementById('back-to-top');
    const tableRows = document.querySelectorAll('tbody tr');
    const filterableItems = document.querySelectorAll('[data-country], [data-tier]');

    // Search functionality
    function handleSearch() {
        const query = searchInput.value.toLowerCase().trim();

        tableRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const isVisible = !query || text.includes(query);
            row.style.display = isVisible ? '' : 'none';
        });
    }

    // Filter functionality
    function handleFilter() {
        const countryValue = countryFilter ? countryFilter.value : '';
        const riskValue = riskFilter ? riskFilter.value : '';

        filterableItems.forEach(item => {
            const country = item.dataset.country || '';
            const tier = item.dataset.tier || '';

            const countryMatch = !countryValue || country === countryValue;
            const riskMatch = !riskValue || tier === riskValue;
            const isVisible = countryMatch && riskMatch;

            item.style.display = isVisible ? '' : 'none';
        });
    }

    // Back to top functionality
    function handleScroll() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        if (backToTopBtn) {
            if (scrollTop > 300) {
                backToTopBtn.classList.add('visible');
            } else {
                backToTopBtn.classList.remove('visible');
            }
        }
    }

    // Smooth scroll to top
    function scrollToTop() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }

    // Event listeners
    if (searchInput) {
        searchInput.addEventListener('input', handleSearch);
    }

    if (searchBtn) {
        searchBtn.addEventListener('click', function(e) {
            e.preventDefault();
            handleSearch();
        });
    }

    if (countryFilter) {
        countryFilter.addEventListener('change', handleFilter);
    }

    if (riskFilter) {
        riskFilter.addEventListener('change', handleFilter);
    }

    if (backToTopBtn) {
        backToTopBtn.addEventListener('click', function(e) {
            e.preventDefault();
            scrollToTop();
        });
    }

    // Scroll event with throttling
    let scrollTimeout;
    window.addEventListener('scroll', function() {
        if (scrollTimeout) {
            clearTimeout(scrollTimeout);
        }
        scrollTimeout = setTimeout(handleScroll, 10);
    });

    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('.toc a');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);

            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Keyboard navigation support
    document.addEventListener('keydown', function(e) {
        // ESC key to clear search and filters
        if (e.key === 'Escape') {
            if (searchInput) searchInput.value = '';
            if (countryFilter) countryFilter.value = '';
            if (riskFilter) riskFilter.value = '';
            handleSearch();
            handleFilter();
        }

        // Enter key in search
        if (e.key === 'Enter' && e.target === searchInput) {
            e.preventDefault();
            handleSearch();
        }
    });

    // Table row hover effects
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.01)';
        });

        row.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });
    });

    // Skip link functionality
    const skipLink = document.querySelector('.skip-link');
    if (skipLink) {
        skipLink.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.getElementById('main-content');
            if (target) {
                target.focus();
                target.scrollIntoView({ behavior: 'smooth' });
            }
        });
    }
});

    /**
     * Set up back to top functionality
     */
    setupBackToTop() {
        if (!this.elements.backToTopBtn) return;

        this.elements.backToTopBtn.addEventListener('click', (e) => {
            e.preventDefault();
            this.scrollToTop();
        });
    }

    /**
     * Set up search functionality
     */
    setupSearch() {
        if (!this.elements.searchInput || !this.elements.searchBtn) return;

        // Search input event
        this.elements.searchInput.addEventListener('input', 
            this.debounce(this.handleSearch.bind(this), 300)
        );

        // Search button event
        this.elements.searchBtn.addEventListener('click', (e) => {
            e.preventDefault();
            this.handleSearch();
        });

        // Enter key support
        this.elements.searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.handleSearch();
            }
        });
    }

    /**
     * Set up filter functionality
     */
    setupFilters() {
        if (this.elements.countryFilter) {
            this.elements.countryFilter.addEventListener('change', this.handleFilter.bind(this));
        }

        if (this.elements.riskFilter) {
            this.elements.riskFilter.addEventListener('change', this.handleFilter.bind(this));
        }
    }

    /**
     * Handle search functionality
     */
    handleSearch() {
        const query = this.elements.searchInput.value.toLowerCase().trim();
        let visibleCount = 0;

        this.elements.tableRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const isVisible = !query || text.includes(query);
            
            this.toggleElement(row, isVisible);
            if (isVisible) visibleCount++;
        });

        // Update search results announcement
        this.announceSearchResults(visibleCount, query);

        // Highlight search terms
        if (query) {
            this.highlightSearchTerms(query);
        } else {
            this.clearHighlights();
        }
    }

    /**
     * Handle filter functionality
     */
    handleFilter() {
        const countryFilter = this.elements.countryFilter?.value || '';
        const riskFilter = this.elements.riskFilter?.value || '';
        let visibleCount = 0;

        this.elements.filterableItems.forEach(item => {
            const country = item.dataset.country || '';
            const tier = item.dataset.tier || '';
            
            const countryMatch = !countryFilter || country === countryFilter;
            const riskMatch = !riskFilter || tier === riskFilter;
            const isVisible = countryMatch && riskMatch;
            
            this.toggleElement(item, isVisible);
            if (isVisible) visibleCount++;
        });

        // Update filter results announcement
        this.announceFilterResults(visibleCount, countryFilter, riskFilter);
    }

    /**
     * Set up accessibility features
     */
    setupAccessibility() {
        // Add ARIA live region for announcements
        this.createLiveRegion();

        // Enhance keyboard navigation
        this.setupKeyboardNavigation();

        // Add focus management
        this.setupFocusManagement();
    }

    /**
     * Create ARIA live region for screen reader announcements
     */
    createLiveRegion() {
        if (document.getElementById('live-region')) return;

        const liveRegion = document.createElement('div');
        liveRegion.id = 'live-region';
        liveRegion.setAttribute('aria-live', 'polite');
        liveRegion.setAttribute('aria-atomic', 'true');
        liveRegion.className = 'sr-only';
        document.body.appendChild(liveRegion);
    }

    /**
     * Set up keyboard navigation
     */
    setupKeyboardNavigation() {
        // Skip link functionality
        const skipLink = document.querySelector('.skip-link');
        if (skipLink) {
            skipLink.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.getElementById('main-content');
                if (target) {
                    target.focus();
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        }

        // Enhanced table navigation
        this.elements.tables.forEach(table => {
            this.enhanceTableAccessibility(table);
        });
    }

    /**
     * Set up focus management
     */
    setupFocusManagement() {
        // Trap focus in modals (if any are added later)
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                // Close any open modals or reset filters
                this.resetFilters();
            }
        });
    }

    /**
     * Set up animations and visual enhancements
     */
    setupAnimations() {
        // Intersection Observer for scroll animations
        if ('IntersectionObserver' in window) {
            this.setupScrollAnimations();
        }

        // Smooth scrolling for navigation links
        this.elements.navLinks.forEach(link => {
            link.addEventListener('click', this.handleSmoothScroll.bind(this));
        });
    }

    /**
     * Set up scroll animations
     */
    setupScrollAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        this.elements.sections.forEach(section => {
            observer.observe(section);
        });
    }

    /**
     * Handle scroll events
     */
    handleScroll() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // Show/hide back to top button
        if (this.elements.backToTopBtn) {
            this.elements.backToTopBtn.classList.toggle('visible', scrollTop > 300);
        }

        // Update active navigation
        this.updateActiveNavigation();
    }

    /**
     * Handle resize events
     */
    handleResize() {
        // Recalculate any layout-dependent features
        this.updateTableResponsiveness();
    }

    /**
     * Smooth scroll to top
     */
    scrollToTop() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }

    /**
     * Handle smooth scrolling for navigation
     */
    handleSmoothScroll(e) {
        e.preventDefault();
        const targetId = e.target.getAttribute('href');
        const targetElement = document.querySelector(targetId);
        
        if (targetElement) {
            targetElement.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
            
            // Update URL without triggering scroll
            history.pushState(null, null, targetId);
            
            // Focus target for accessibility
            targetElement.focus();
        }
    }

    /**
     * Update active navigation based on scroll position
     */
    updateActiveNavigation() {
        const sections = Array.from(this.elements.sections);
        const navLinks = Array.from(this.elements.navLinks);
        
        let currentSection = '';
        
        sections.forEach(section => {
            const rect = section.getBoundingClientRect();
            if (rect.top <= 100 && rect.bottom >= 100) {
                currentSection = section.id;
            }
        });
        
        navLinks.forEach(link => {
            const href = link.getAttribute('href').substring(1);
            link.classList.toggle('active', href === currentSection);
        });
    }

    /**
     * Enhance table accessibility
     */
    enhanceTableAccessibility(table) {
        // Add table caption if missing
        if (!table.querySelector('caption')) {
            const caption = document.createElement('caption');
            caption.textContent = table.getAttribute('aria-label') || '数据表格';
            caption.className = 'sr-only';
            table.insertBefore(caption, table.firstChild);
        }

        // Add sortable functionality (basic)
        const headers = table.querySelectorAll('th');
        headers.forEach((header, index) => {
            if (index === 0) return; // Skip first column (names)
            
            header.style.cursor = 'pointer';
            header.setAttribute('tabindex', '0');
            header.setAttribute('role', 'button');
            header.setAttribute('aria-label', `排序 ${header.textContent}`);
            
            header.addEventListener('click', () => this.sortTable(table, index));
            header.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.sortTable(table, index);
                }
            });
        });
    }

    /**
     * Basic table sorting
     */
    sortTable(table, columnIndex) {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));

        rows.sort((a, b) => {
            const aText = a.cells[columnIndex].textContent.trim();
            const bText = b.cells[columnIndex].textContent.trim();
            return aText.localeCompare(bText, 'zh-CN');
        });

        rows.forEach(row => tbody.appendChild(row));

        // Announce sort completion
        this.announce(`表格已按第${columnIndex + 1}列排序`);
    }

    /**
     * Set up table enhancements
     */
    setupTableEnhancements() {
        this.elements.tables.forEach(table => {
            this.addTableFeatures(table);
        });
    }

    /**
     * Add enhanced features to tables
     */
    addTableFeatures(table) {
        // Add row hover effects
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            row.addEventListener('mouseenter', () => {
                row.style.transform = 'scale(1.01)';
            });

            row.addEventListener('mouseleave', () => {
                row.style.transform = '';
            });
        });

        // Add click to highlight functionality
        rows.forEach(row => {
            row.addEventListener('click', () => {
                rows.forEach(r => r.classList.remove('highlighted'));
                row.classList.add('highlighted');
            });
        });
    }

    /**
     * Update table responsiveness
     */
    updateTableResponsiveness() {
        this.elements.tables.forEach(table => {
            const container = table.closest('.table-container');
            if (container) {
                const isOverflowing = table.scrollWidth > container.clientWidth;
                container.classList.toggle('scrollable', isOverflowing);
            }
        });
    }

    /**
     * Highlight search terms in content
     */
    highlightSearchTerms(query) {
        this.clearHighlights();

        if (!query || query.length < 2) return;

        const walker = document.createTreeWalker(
            document.body,
            NodeFilter.SHOW_TEXT,
            {
                acceptNode: (node) => {
                    return node.parentElement.tagName !== 'SCRIPT' &&
                           node.parentElement.tagName !== 'STYLE' &&
                           !node.parentElement.classList.contains('search-container')
                        ? NodeFilter.FILTER_ACCEPT
                        : NodeFilter.FILTER_REJECT;
                }
            }
        );

        const textNodes = [];
        let node;
        while (node = walker.nextNode()) {
            if (node.textContent.toLowerCase().includes(query)) {
                textNodes.push(node);
            }
        }

        textNodes.forEach(textNode => {
            const parent = textNode.parentElement;
            const text = textNode.textContent;
            const regex = new RegExp(`(${this.escapeRegex(query)})`, 'gi');
            const highlightedText = text.replace(regex, '<mark class="search-highlight">$1</mark>');

            if (highlightedText !== text) {
                const wrapper = document.createElement('span');
                wrapper.innerHTML = highlightedText;
                parent.replaceChild(wrapper, textNode);
            }
        });
    }

    /**
     * Clear search highlights
     */
    clearHighlights() {
        const highlights = document.querySelectorAll('.search-highlight');
        highlights.forEach(highlight => {
            const parent = highlight.parentElement;
            parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
            parent.normalize();
        });
    }

    /**
     * Toggle element visibility
     */
    toggleElement(element, isVisible) {
        if (isVisible) {
            element.style.display = '';
            element.classList.remove('hidden');
        } else {
            element.style.display = 'none';
            element.classList.add('hidden');
        }
    }

    /**
     * Reset all filters
     */
    resetFilters() {
        if (this.elements.searchInput) this.elements.searchInput.value = '';
        if (this.elements.countryFilter) this.elements.countryFilter.value = '';
        if (this.elements.riskFilter) this.elements.riskFilter.value = '';

        this.elements.filterableItems.forEach(item => {
            this.toggleElement(item, true);
        });

        this.clearHighlights();
        this.announce('所有筛选条件已重置');
    }

    /**
     * Set up service worker for PWA functionality
     */
    setupServiceWorker() {
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    }

    /**
     * Announce message to screen readers
     */
    announce(message) {
        const liveRegion = document.getElementById('live-region');
        if (liveRegion) {
            liveRegion.textContent = message;
            setTimeout(() => {
                liveRegion.textContent = '';
            }, 1000);
        }
    }

    /**
     * Announce search results
     */
    announceSearchResults(count, query) {
        if (query) {
            this.announce(`搜索"${query}"找到${count}个结果`);
        } else {
            this.announce('搜索已清除，显示所有结果');
        }
    }

    /**
     * Announce filter results
     */
    announceFilterResults(count, country, risk) {
        let message = `筛选结果：${count}个项目`;
        if (country) message += `，国家：${country}`;
        if (risk) message += `，风险等级：${risk}`;
        this.announce(message);
    }

    /**
     * Announce page load
     */
    announcePageLoad() {
        this.announce('海外教育风险识别指南已加载完成');
    }

    /**
     * Utility: Throttle function
     */
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * Utility: Debounce function
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Utility: Escape regex special characters
     */
    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    /**
     * Utility: Check if element is in viewport
     */
    isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    /**
     * Export data functionality (for future enhancement)
     */
    exportData(format = 'json') {
        const data = this.collectTableData();

        switch (format) {
            case 'json':
                this.downloadJSON(data);
                break;
            case 'csv':
                this.downloadCSV(data);
                break;
            default:
                console.warn('Unsupported export format:', format);
        }
    }

    /**
     * Collect data from tables
     */
    collectTableData() {
        const data = [];

        this.elements.tables.forEach(table => {
            const headers = Array.from(table.querySelectorAll('th')).map(th => th.textContent.trim());
            const rows = Array.from(table.querySelectorAll('tbody tr'));

            rows.forEach(row => {
                const cells = Array.from(row.querySelectorAll('td'));
                const rowData = {};

                cells.forEach((cell, index) => {
                    if (headers[index]) {
                        rowData[headers[index]] = cell.textContent.trim();
                    }
                });

                data.push(rowData);
            });
        });

        return data;
    }

    /**
     * Download data as JSON
     */
    downloadJSON(data) {
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        this.downloadBlob(blob, 'education-risk-data.json');
    }

    /**
     * Download data as CSV
     */
    downloadCSV(data) {
        if (data.length === 0) return;

        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        this.downloadBlob(blob, 'education-risk-data.csv');
    }

    /**
     * Download blob as file
     */
    downloadBlob(blob, filename) {
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        URL.revokeObjectURL(url);
    }
}

// Initialize the application
const educationGuide = new EducationGuide();

// Export for potential module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EducationGuide;
}
