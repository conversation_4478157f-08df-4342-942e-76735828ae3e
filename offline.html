<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>离线模式 - 海外教育风险识别指南</title>
    <style>
        :root {
            --primary-accent: #ff9900;
            --background-dark: #1a1a1a;
            --background-card: #2c2c2c;
            --text-light: #f5f5f5;
            --text-secondary: #a0a0a0;
            --border-color: #444444;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background-color: var(--background-dark);
            color: var(--text-light);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .offline-container {
            max-width: 600px;
            text-align: center;
            background: var(--background-card);
            padding: 3rem 2rem;
            border-radius: 1rem;
            border: 1px solid var(--border-color);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .offline-icon {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            opacity: 0.7;
        }

        h1 {
            font-size: 2rem;
            color: var(--primary-accent);
            margin-bottom: 1rem;
        }

        p {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .offline-actions {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-top: 2rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: var(--primary-accent);
            color: #000;
        }

        .btn-primary:hover {
            background: #ffaf33;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: transparent;
            color: var(--primary-accent);
            border: 2px solid var(--primary-accent);
        }

        .btn-secondary:hover {
            background: var(--primary-accent);
            color: #000;
        }

        .offline-tips {
            margin-top: 2rem;
            padding: 1.5rem;
            background: rgba(255, 153, 0, 0.1);
            border-radius: 0.5rem;
            border-left: 4px solid var(--primary-accent);
            text-align: left;
        }

        .offline-tips h3 {
            color: var(--primary-accent);
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .offline-tips ul {
            list-style: none;
            padding: 0;
        }

        .offline-tips li {
            margin-bottom: 0.5rem;
            padding-left: 1.5rem;
            position: relative;
        }

        .offline-tips li::before {
            content: '•';
            position: absolute;
            left: 0;
            color: var(--primary-accent);
            font-weight: bold;
        }

        .connection-status {
            margin-top: 1rem;
            padding: 0.75rem;
            border-radius: 0.5rem;
            font-weight: 600;
        }

        .status-offline {
            background: rgba(255, 107, 107, 0.2);
            color: #ff6b6b;
            border: 1px solid #ff6b6b;
        }

        .status-online {
            background: rgba(92, 184, 92, 0.2);
            color: #5cb85c;
            border: 1px solid #5cb85c;
        }

        @media (max-width: 480px) {
            .offline-container {
                padding: 2rem 1rem;
            }
            
            h1 {
                font-size: 1.5rem;
            }
            
            .offline-icon {
                font-size: 3rem;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">📡</div>
        <h1>当前处于离线模式</h1>
        <p>您的网络连接似乎出现了问题，但您仍然可以浏览已缓存的内容。</p>
        
        <div class="connection-status" id="connection-status">
            <span id="status-text">检查网络连接中...</span>
        </div>

        <div class="offline-actions">
            <button class="btn btn-primary" onclick="window.location.reload()">
                重新加载页面
            </button>
            <a href="/" class="btn btn-secondary">
                返回首页
            </a>
        </div>

        <div class="offline-tips">
            <h3>离线使用提示</h3>
            <ul>
                <li>您可以继续浏览已访问过的页面内容</li>
                <li>搜索和筛选功能在离线状态下仍然可用</li>
                <li>外部链接需要网络连接才能访问</li>
                <li>恢复网络连接后，页面将自动更新最新内容</li>
            </ul>
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connection-status');
            const statusText = document.getElementById('status-text');
            
            if (navigator.onLine) {
                statusElement.className = 'connection-status status-online';
                statusText.textContent = '网络连接已恢复';
                
                // Auto reload after 2 seconds if online
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                statusElement.className = 'connection-status status-offline';
                statusText.textContent = '当前离线状态';
            }
        }

        // Listen for connection changes
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);

        // Initial check
        updateConnectionStatus();

        // Periodic check
        setInterval(() => {
            // Try to fetch a small resource to verify connection
            fetch('/favicon.ico', { 
                method: 'HEAD',
                cache: 'no-cache'
            })
            .then(() => {
                if (!navigator.onLine) {
                    // Force online status if fetch succeeds
                    window.dispatchEvent(new Event('online'));
                }
            })
            .catch(() => {
                if (navigator.onLine) {
                    // Force offline status if fetch fails
                    window.dispatchEvent(new Event('offline'));
                }
            });
        }, 5000);

        // Add keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                const focused = document.activeElement;
                if (focused && focused.classList.contains('btn')) {
                    focused.click();
                }
            }
        });

        // Add retry functionality
        let retryCount = 0;
        const maxRetries = 3;

        function retryConnection() {
            if (retryCount < maxRetries) {
                retryCount++;
                const statusText = document.getElementById('status-text');
                statusText.textContent = `重试连接中... (${retryCount}/${maxRetries})`;
                
                setTimeout(() => {
                    fetch('/', { 
                        method: 'HEAD',
                        cache: 'no-cache'
                    })
                    .then(() => {
                        window.location.reload();
                    })
                    .catch(() => {
                        if (retryCount >= maxRetries) {
                            statusText.textContent = '连接失败，请检查网络设置';
                        } else {
                            retryConnection();
                        }
                    });
                }, 2000);
            }
        }

        // Start retry process after 5 seconds
        setTimeout(retryConnection, 5000);
    </script>
</body>
</html>
